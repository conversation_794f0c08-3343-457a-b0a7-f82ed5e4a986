#!/usr/bin/env python3
"""
训练脚本，确保保存到RustFS
基于现有pipeline流程，增加RustFS存储
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from loguru import logger
from src.innovation_model_investigation.api.services.core_training import CoreTrainingEngine
from src.innovation_model_investigation.api.utils.unified_storage import UnifiedModelStorage


async def main():
    """主函数"""
    logger.info("🚀 开始训练并保存到RustFS")
    
    try:
        # 初始化核心训练引擎
        engine = CoreTrainingEngine()
        
        # 初始化统一存储（强制启用RustFS）
        storage = UnifiedModelStorage(base_dir="outputs", use_rustfs=True)
        
        # 执行训练
        version, result = await engine.execute_training_with_pipeline(
            output_dir=Path("outputs")
        )
        
        # 强制保存到RustFS
        logger.info("💾 强制保存到统一存储（包括RustFS）...")
        storage.save_model_version(
            version=version,
            files=result["files"],
            metadata=result["metadata"]
        )
        
        # 验证保存结果
        logger.info("🔍 验证保存结果...")
        models = storage.list_models()
        logger.info(f"📋 找到 {len(models)} 个模型:")
        for model in models:
            logger.info(f"  - 版本: {model.get('version')}, 来源: {model.get('source')}, 创建时间: {model.get('created_at')}")

        rustfs_models = [m for m in models if m.get("source") == "rustfs"]
        local_models = [m for m in models if m.get("source") == "local"]

        logger.info(f"📊 模型统计: 本地={len(local_models)}, RustFS={len(rustfs_models)}")

        if rustfs_models:
            logger.info(f"✅ RustFS保存成功！找到 {len(rustfs_models)} 个RustFS模型")
            for model in rustfs_models:
                logger.info(f"  - 版本: {model.get('version')}, 创建时间: {model.get('created_at')}")
        else:
            logger.warning("⚠️ 未找到RustFS模型，但本地保存成功")
        
        logger.info(f"🎉 训练完成: 版本={version}")
        
    except Exception as e:
        logger.exception(f"❌ 训练失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())